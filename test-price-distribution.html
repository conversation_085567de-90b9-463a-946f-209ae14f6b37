<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>价格分布测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .distribution-chart {
            display: flex;
            align-items: end;
            justify-content: space-between;
            height: 200px;
            background: #f5f5f5;
            padding: 10px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .bar {
            background: #3b82f6;
            border-radius: 2px;
            min-width: 8px;
            margin: 0 2px;
            transition: all 0.3s ease;
        }
        .bar:hover {
            background: #1d4ed8;
        }
        .stats {
            background: #f9f9f9;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .error {
            color: red;
            background: #fee;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            color: green;
            background: #efe;
            padding: 10px;
            border-radius: 4px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #1d4ed8;
        }
    </style>
</head>
<body>
    <h1>价格分布功能测试</h1>
    
    <div>
        <button onclick="testAPI()">测试API</button>
        <button onclick="testCNYDistribution()">测试CNY分布</button>
        <button onclick="testUSDDistribution()">测试USD分布</button>
    </div>
    
    <div id="result"></div>
    
    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在测试API...</p>';
            
            try {
                const response = await fetch('http://localhost:3000/api/v1/stats/overview');
                const data = await response.json();
                
                if (data.success) {
                    const priceStats = data.data.priceStats;
                    resultDiv.innerHTML = `
                        <div class="success">✅ API调用成功</div>
                        <div class="stats">
                            <h3>价格统计信息</h3>
                            <p><strong>CNY价格范围:</strong> ¥${priceStats.min} - ¥${priceStats.max}</p>
                            <p><strong>CNY平均价格:</strong> ¥${priceStats.average}</p>
                            <p><strong>CNY分布数据:</strong> ${priceStats.distribution ? '✅ 有数据' : '❌ 无数据'}</p>
                            <p><strong>USD价格范围:</strong> $${priceStats.minUSD} - $${priceStats.maxUSD}</p>
                            <p><strong>USD平均价格:</strong> $${priceStats.averageUSD}</p>
                            <p><strong>USD分布数据:</strong> ${priceStats.distributionUSD ? '✅ 有数据' : '❌ 无数据'}</p>
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `<div class="error">❌ API调用失败: ${data.message}</div>`;
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 网络错误: ${error.message}</div>`;
            }
        }
        
        async function testCNYDistribution() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在获取CNY价格分布...</p>';
            
            try {
                const response = await fetch('http://localhost:3000/api/v1/stats/overview');
                const data = await response.json();
                
                if (data.success && data.data.priceStats.distribution) {
                    const distribution = data.data.priceStats.distribution;
                    const maxCount = Math.max(...distribution);
                    
                    let chartHTML = '<div class="success">✅ CNY价格分布数据</div>';
                    chartHTML += '<div class="distribution-chart">';
                    
                    distribution.forEach((count, index) => {
                        const height = maxCount > 0 ? (count / maxCount) * 180 : 0;
                        chartHTML += `<div class="bar" style="height: ${height}px" title="区间${index + 1}: ${count}个产品"></div>`;
                    });
                    
                    chartHTML += '</div>';
                    chartHTML += `<p>总计: ${distribution.reduce((a, b) => a + b, 0)} 个产品</p>`;
                    
                    resultDiv.innerHTML = chartHTML;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 无法获取CNY分布数据</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 错误: ${error.message}</div>`;
            }
        }
        
        async function testUSDDistribution() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>正在获取USD价格分布...</p>';
            
            try {
                const response = await fetch('http://localhost:3000/api/v1/stats/overview');
                const data = await response.json();
                
                if (data.success && data.data.priceStats.distributionUSD) {
                    const distribution = data.data.priceStats.distributionUSD;
                    const maxCount = Math.max(...distribution);
                    
                    let chartHTML = '<div class="success">✅ USD价格分布数据</div>';
                    chartHTML += '<div class="distribution-chart">';
                    
                    distribution.forEach((count, index) => {
                        const height = maxCount > 0 ? (count / maxCount) * 180 : 0;
                        chartHTML += `<div class="bar" style="height: ${height}px" title="区间${index + 1}: ${count}个产品"></div>`;
                    });
                    
                    chartHTML += '</div>';
                    chartHTML += `<p>总计: ${distribution.reduce((a, b) => a + b, 0)} 个产品</p>`;
                    
                    resultDiv.innerHTML = chartHTML;
                } else {
                    resultDiv.innerHTML = '<div class="error">❌ 无法获取USD分布数据</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 错误: ${error.message}</div>`;
            }
        }
        
        // 页面加载时自动测试API
        window.onload = function() {
            testAPI();
        };
    </script>
</body>
</html>
