# 价格筛选器国际化问题修复测试指南

## 修复内容总结

1. **后端更新**：
   - 更新了 `/stats/overview` 接口，增加了 USD 价格统计（minUSD, maxUSD, averageUSD, medianUSD）
   - 后端现在同时返回 CNY 和 USD 的价格统计数据

2. **前端更新**：
   - FilterPanel 组件根据当前语言选择正确的价格范围（英文使用 USD，中文使用 CNY）
   - PriceRangeQuickSelect 组件现在根据实际价格范围动态生成快速选择按钮
   - 价格筛选时会自动进行货币转换（英文界面下将 USD 转换为 CNY 发送给后端）

## 测试步骤

### 1. 启动服务
```bash
# 启动后端服务
cd products-backend
npm run dev

# 启动前端服务
cd product-showcase
npm run dev
```

### 2. 测试中文界面
1. 访问 http://localhost:5173
2. 确保语言设置为中文
3. 打开价格筛选器
4. 检查：
   - 价格范围显示为人民币（¥）
   - 最大值应该显示实际产品的最高价格（而不是固定的 450）
   - 快速选择按钮应该根据实际价格范围动态生成

### 3. 测试英文界面
1. 切换语言到英文（点击右上角语言切换按钮）
2. 打开价格筛选器
3. 检查：
   - 价格范围显示为美元（$）
   - 最大值应该显示实际产品的最高美元价格
   - 快速选择按钮应该显示合理的美元价格段

### 4. 测试价格筛选功能
1. 在英文界面下，选择一个价格范围（如 $10-$50）
2. 点击应用筛选
3. 检查：
   - 产品列表应该只显示价格在 $10-$50 之间的产品
   - URL 参数应该更新

### 5. 测试价格转换
1. 在英文界面下设置价格筛选（如 $20-$60）
2. 切换到中文界面
3. 检查：
   - 价格筛选器应该显示对应的人民币价格（约 ¥140-¥420）
   - 产品列表筛选结果应该保持一致

## 预期结果

- ✅ 中文界面显示人民币价格，最大值基于实际产品数据
- ✅ 英文界面显示美元价格，最大值基于实际产品的 USD 价格数据
- ✅ 价格筛选功能在两种语言下都能正常工作
- ✅ 快速选择按钮根据实际价格范围动态生成，不再是硬编码的值
- ✅ 语言切换时价格显示自动更新为对应货币

## 技术细节

- 后端始终使用 CNY 价格进行筛选
- 前端在英文界面下会自动将用户选择的 USD 价格转换为 CNY 发送给后端
- 汇率设置为 1 USD = 6.99 CNY（可在 `utils/priceConversion.ts` 中调整）