{"field_count": 40, "record_count": 0, "fields": {"采集平台": {"id": "fldlTALTDP", "type": 3, "description": ""}, "品名": {"id": "fld98c3F01", "type": 3, "description": ""}, "产品品名": {"id": "fldEPFf9lm", "type": 20, "description": ""}, "采集时间": {"id": "fldlyJcXRn", "type": 1001, "description": ""}, "品类一级": {"id": "fldGtFPP20", "type": 3, "description": ""}, "品类二级": {"id": "fldrfy01PS", "type": 3, "description": ""}, "Single/Mixed": {"id": "fldr1j3u4f", "type": 3, "description": ""}, "口味": {"id": "fld6dbQGAn", "type": 1, "description": ""}, "Specs(规格)": {"id": "fldmUt5qWm", "type": 1, "description": ""}, "CTN(箱规)": {"id": "fld7HdKvwS", "type": 1, "description": ""}, "正常售价": {"id": "fldLtVHZ5b", "type": 2, "description": ""}, "优惠到手价": {"id": "fldGvzGGFG", "type": 2, "description": ""}, "Origin (Country)": {"id": "fldkZNReiw", "type": 3, "description": ""}, "Origin (Province)": {"id": "fldpRMAAXr", "type": 4, "description": ""}, "Origin (City)": {"id": "fldisZBrD1", "type": 4, "description": ""}, "Manufacturer(生产商)": {"id": "fldEFufAf2", "type": 1, "description": ""}, "Client(委托方)": {"id": "fldx4OdUsm", "type": 1, "description": ""}, "Gift(赠品)": {"id": "fldcfIZwSn", "type": 1, "description": ""}, "Gift mechanism(赠品机制)": {"id": "fldGrxT34A", "type": 1, "description": ""}, "商品链接": {"id": "fldUZibVDt", "type": 15, "description": ""}, "Front image(正)": {"id": "fldRZvGjSK", "type": 17, "description": ""}, "备注": {"id": "fldwWN61Y0", "type": 1, "description": ""}, "Back image(背)": {"id": "fldhXyI07b", "type": 17, "description": ""}, "Tag photo(标签)": {"id": "fldGLGCv2m", "type": 17, "description": ""}, "Outer packaging image(外包装)": {"id": "fldkUCi2Vh", "type": 17, "description": ""}, "Gift pictures(赠品图片)": {"id": "fldC0kw9Hh", "type": 17, "description": ""}, "序号3": {"id": "fldNTalSuy", "type": 1, "description": ""}, "序号2": {"id": "fld2vxWg3B", "type": 1, "description": ""}, "序号1": {"id": "fldwQnkzrl", "type": 20, "description": ""}, "序号": {"id": "fldRW7Bszz", "type": 20, "description": ""}, "编号": {"id": "fldZW4Q5I2", "type": 1005, "description": ""}, "Price（USD）": {"id": "fld19OLKKG", "type": 20, "description": ""}, "Special Price（USD）": {"id": "fldfP2hZIB", "type": 20, "description": ""}, "Product Name": {"id": "fldJZWSqLX", "type": 1, "description": ""}, "Flavor(口味)": {"id": "fldhkuLoKJ", "type": 1, "description": ""}, "Category Level 1": {"id": "fldoD52TeP", "type": 19, "description": ""}, "Category Level 2": {"id": "fldxk3XteX", "type": 1, "description": ""}, "bar code(条码)": {"id": "fldFeNTpIL", "type": 1, "description": ""}, "rx编号": {"id": "fldsbenBWp", "type": 1005, "description": ""}, "Platform(平台)": {"id": "fldkuD0wjJ", "type": 19, "description": ""}}, "sample_record": null, "field_types": {"3": 6, "20": 5, "1001": 1, "1": 14, "2": 2, "4": 2, "15": 1, "17": 5, "1005": 2, "19": 2}, "image_fields": [{"name": "Front image(正)", "id": "fldRZvGjSK"}, {"name": "Back image(背)", "id": "fldhXyI07b"}, {"name": "Tag photo(标签)", "id": "fldGLGCv2m"}, {"name": "Outer packaging image(外包装)", "id": "fldkUCi2Vh"}, {"name": "Gift pictures(赠品图片)", "id": "fldC0kw9Hh"}]}